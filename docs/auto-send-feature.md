# Auto-Send Feature Documentation

## Overview

The auto-send feature automatically sends chat messages after a period of user inactivity, providing a seamless user experience in the AI interview interface.

## How It Works

### Timer Logic
1. **Silent Period (5 seconds)**: When the user stops typing or speaking, a 5-second silent period begins
2. **Countdown Period (10 seconds)**: After the silent period, a visible 10-second countdown timer appears
3. **Auto-Send**: If no user interaction occurs during the countdown, the message is automatically sent
4. **Reset Behavior**: Any text change (typing or voice input) resets both timers and starts from the beginning

### Total Timeout
- **Total Time**: 15 seconds (5 seconds silent + 10 seconds countdown)
- **Configurable**: Timer values can be adjusted in `src/constants/speech-recognition-config.ts`

## Implementation Details

### Files Modified/Created

1. **`src/constants/speech-recognition-config.ts`**
   - Added auto-send configuration constants
   - `AUTO_SEND_SILENT_PERIOD`: 5000ms
   - `AUTO_SEND_COUNTDOWN_PERIOD`: 10000ms

2. **`src/hooks/use-auto-send.ts`**
   - Custom hook managing auto-send timer logic
   - Handles silent period and countdown timers
   - Provides reset functionality and countdown state

3. **`src/components/interview/auto-send-timer.tsx`**
   - Visual countdown timer component
   - Displays in top-right corner during countdown
   - Includes progress indicator and cancel instructions

4. **`src/components/interview/speech-recognition-input.tsx`**
   - Integrated auto-send functionality
   - Resets timers on text changes and voice input
   - Displays auto-send timer component

### Key Features

#### Text Change Detection
- Monitors textarea input changes
- Resets timer on any character modification
- Works with both manual typing and voice-to-text

#### Voice Integration
- Compatible with existing speech recognition
- Resets timer when voice input is received
- Maintains all existing microphone functionality

#### Visual Feedback
- Prominent countdown timer display
- Progress circle showing remaining time
- Clear instructions for canceling auto-send

#### User Experience
- Non-intrusive during silent period
- Clear visual feedback during countdown
- Easy cancellation by typing or speaking

## Usage

### Basic Integration
```tsx
import { SpeechRecognitionInput } from "@/components/interview/speech-recognition-input";

function ChatInterface() {
  const [message, setMessage] = useState("");
  
  const handleSend = () => {
    // Handle message sending
    console.log("Sending:", message);
    setMessage("");
  };

  return (
    <SpeechRecognitionInput
      value={message}
      onChange={setMessage}
      onSend={handleSend}
      placeholder="Type your message..."
    />
  );
}
```

### Configuration
Adjust timer values in `src/constants/speech-recognition-config.ts`:

```typescript
export const AUTO_SEND_SILENT_PERIOD = 5000; // 5 seconds
export const AUTO_SEND_COUNTDOWN_PERIOD = 10000; // 10 seconds
```

## Testing

### Test Page
A dedicated test page is available at `/test-auto-send` to verify functionality:

1. Type a message and stop typing
2. Wait 5 seconds for countdown to appear
3. Observe 10-second countdown timer
4. Test cancellation by typing during countdown
5. Verify voice input integration

### Manual Testing Steps
1. **Basic Auto-Send**: Type message → wait 15 seconds → verify auto-send
2. **Cancellation**: Type message → wait 7 seconds → type again → verify timer reset
3. **Voice Integration**: Use voice input → wait → verify timer behavior
4. **Edge Cases**: Empty messages, disabled state, rapid typing

## Browser Compatibility

- Works in all modern browsers
- Graceful degradation if speech recognition unavailable
- No external dependencies required

## Performance Considerations

- Efficient timer management with proper cleanup
- Minimal re-renders using React hooks
- Debounced text change detection
- Memory leak prevention with useEffect cleanup

## Accessibility

- Visual countdown timer with clear indicators
- Keyboard navigation support
- Screen reader compatible
- High contrast design for visibility

## Future Enhancements

- Configurable timer values via UI settings
- Audio notifications for countdown
- Customizable countdown position
- Integration with user preferences
